# 售票记录Word导出功能使用指南

## 功能概述

本功能用于将售票记录数据导出为Word文档(.docx格式)，生成的文档包含：
1. 基本信息表格（机型、注册号、飞行日期）
2. 天气信息表格（始发地和目的地的天气数据）
3. 动态信息表格（飞行时刻和时间统计）

## 技术架构

### 后端组件
- **DTO**: `TicketRecordExportDto` - 数据传输对象
- **Service**: `ITicketRecordExportService` - 服务接口
- **ServiceImpl**: `TicketRecordExportServiceImpl` - 服务实现
- **Controller**: `TicketRecordExportController` - 控制器

### 依赖库
- Apache POI 4.1.2 - Word文档生成
- Spring Boot - Web框架
- Lombok - 简化代码

## API接口

### 导出接口
```
POST /sgui-export/v2/ticket-record/export-word
Content-Type: application/json
```

### 请求数据结构
```json
{
  "aircraftModel": "BELL429",
  "registrationNumber": "B-7613", 
  "flightDate": "2025-05-20",
  "weatherInfoList": [
    {
      "sortOrder": 1,
      "departureAirport": "呈贡",
      "weather": "晴天",
      "cloudHeight": "无影响",
      "temperature": "26",
      "windDirection": "100",
      "windSpeed": "4",
      "visibility": "9999",
      "qnh": "1004",
      "runway": "徐建军",
      "sideRunway": "王昂",
      "arrivalAirport": "呈贡",
      "arrivalWeather": "晴天",
      "arrivalCloudHeight": "无影响",
      "arrivalTemperature": "26",
      "arrivalWindDirection": "100",
      "arrivalWindSpeed": "4",
      "arrivalVisibility": "9999",
      "arrivalQnh": "1004",
      "arrivalRunway": "徐建军",
      "arrivalSideRunway": "王昂"
    }
  ],
  "dynamicInfoList": [
    {
      "sortOrder": 1,
      "departureAirport": "呈贡",
      "arrivalAirport": "呈贡",
      "scheduledDepartureTime": "19:43",
      "actualDepartureTime": "19:47",
      "scheduledArrivalTime": "20:03",
      "actualArrivalTime": "20:05",
      "groundTimeMinutes": 6,
      "flightTimeMinutes": 16,
      "totalTimeMinutes": 22,
      "legNumber": 1
    }
  ]
}
```

## 前端集成

### JavaScript调用示例
```javascript
// 引入导出脚本
<script src="/js/ticket-record-export.js"></script>

// 调用导出功能
exportTicketRecordToWord(exportData);
```

### HTML按钮示例
```html
<button onclick="exportExample()">导出Word文档</button>
```

## 测试

### 运行单元测试
```bash
mvn test -Dtest=TicketRecordExportServiceTest
```

### 测试步骤
1. 运行测试类 `TicketRecordExportServiceTest`
2. 检查生成的 `test_ticket_record.docx` 文件
3. 验证文档格式和内容是否正确

## 部署说明

### 依赖检查
确保项目已包含Apache POI依赖：
```xml
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>4.1.2</version>
</dependency>
```

### 配置要求
- JDK 1.8+
- Spring Boot 2.5.15
- 足够的内存空间用于文档生成

## 常见问题

### Q: 导出的Word文档无法打开？
A: 检查Apache POI版本兼容性，确保使用4.1.2或更高版本。

### Q: 中文字符显示异常？
A: 确保字体设置为"宋体"，并检查编码设置。

### Q: 表格格式不正确？
A: 检查数据结构是否完整，特别是weatherInfoList和dynamicInfoList。

### Q: 内存溢出？
A: 对于大量数据，考虑分批处理或增加JVM内存配置。

## 扩展功能

### 自定义样式
可以在 `TicketRecordExportServiceImpl` 中修改：
- 字体大小和样式
- 表格边框和颜色
- 页面布局和间距

### 添加图片
```java
// 在文档中添加图片
XWPFRun run = paragraph.createRun();
run.addPicture(inputStream, XWPFDocument.PICTURE_TYPE_PNG, "image.png", 
               Units.toEMU(200), Units.toEMU(200));
```

### 添加页眉页脚
```java
// 添加页眉
XWPFHeader header = document.createHeader(HeaderFooterType.DEFAULT);
XWPFParagraph headerParagraph = header.createParagraph();
headerParagraph.createRun().setText("售票记录页");
```

## 维护建议

1. 定期更新Apache POI版本以获得最新功能和安全修复
2. 监控导出功能的性能，特别是大数据量场景
3. 备份重要的导出模板和配置
4. 建立日志监控机制，及时发现和解决问题
