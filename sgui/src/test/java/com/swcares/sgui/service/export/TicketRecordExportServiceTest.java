package com.swcares.sgui.service.export;

import com.swcares.sgui.obj.dto.TicketRecordExportDto;
import com.swcares.sgui.service.export.impl.TicketRecordExportServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 售票记录导出服务测试类
 * <AUTHOR> Assistant
 * @date 2025/01/30 10:30
 */
@SpringBootTest
public class TicketRecordExportServiceTest {

    @Test
    public void testGenerateWordDocument() throws IOException {
        TicketRecordExportServiceImpl service = new TicketRecordExportServiceImpl();
        
        // 构造测试数据
        TicketRecordExportDto exportData = createTestData();
        
        // 生成Word文档
        byte[] documentBytes = service.generateWordDocument(exportData);
        
        // 保存到文件进行验证
        try (FileOutputStream fos = new FileOutputStream("test_ticket_record_merged.docx")) {
            fos.write(documentBytes);
        }

        System.out.println("合并表格Word文档生成成功，文件大小：" + documentBytes.length + " 字节");
    }

    /**
     * 创建测试数据
     */
    private TicketRecordExportDto createTestData() {
        TicketRecordExportDto exportData = new TicketRecordExportDto();
        exportData.setAircraftModel("BELL429");
        exportData.setRegistrationNumber("B-7613");
        exportData.setFlightDate("2025-05-20");
        
        // 创建始发地天气信息
        List<TicketRecordExportDto.DepartureWeatherInfo> departureWeatherInfoList = new ArrayList<>();
        TicketRecordExportDto.DepartureWeatherInfo departureWeatherInfo = new TicketRecordExportDto.DepartureWeatherInfo();
        departureWeatherInfo.setSortOrder(1);
        departureWeatherInfo.setAirport("呈贡");
        departureWeatherInfo.setWeather("晴天");
        departureWeatherInfo.setCloudHeight("无影响");
        departureWeatherInfo.setTemperature("26");
        departureWeatherInfo.setWindDirection("100");
        departureWeatherInfo.setWindSpeed("4");
        departureWeatherInfo.setVisibility("9999");
        departureWeatherInfo.setQnh("1004");
        departureWeatherInfo.setRunway("徐建军");
        departureWeatherInfo.setSideRunway("王昂");
        departureWeatherInfoList.add(departureWeatherInfo);
        exportData.setDepartureWeatherInfoList(departureWeatherInfoList);

        // 创建目的地天气信息
        List<TicketRecordExportDto.ArrivalWeatherInfo> arrivalWeatherInfoList = new ArrayList<>();
        TicketRecordExportDto.ArrivalWeatherInfo arrivalWeatherInfo = new TicketRecordExportDto.ArrivalWeatherInfo();
        arrivalWeatherInfo.setSortOrder(1);
        arrivalWeatherInfo.setAirport("呈贡");
        arrivalWeatherInfo.setWeather("晴天");
        arrivalWeatherInfo.setCloudHeight("无影响");
        arrivalWeatherInfo.setTemperature("26");
        arrivalWeatherInfo.setWindDirection("100");
        arrivalWeatherInfo.setWindSpeed("4");
        arrivalWeatherInfo.setVisibility("9999");
        arrivalWeatherInfo.setQnh("1004");
        arrivalWeatherInfo.setRunway("徐建军");
        arrivalWeatherInfo.setSideRunway("王昂");
        arrivalWeatherInfoList.add(arrivalWeatherInfo);
        exportData.setArrivalWeatherInfoList(arrivalWeatherInfoList);
        
        // 创建动态信息
        List<TicketRecordExportDto.DynamicInfo> dynamicInfoList = new ArrayList<>();
        
        TicketRecordExportDto.DynamicInfo dynamicInfo1 = new TicketRecordExportDto.DynamicInfo();
        dynamicInfo1.setSortOrder(1);
        dynamicInfo1.setDepartureAirport("呈贡");
        dynamicInfo1.setArrivalAirport("呈贡");
        dynamicInfo1.setScheduledDepartureTime("19:43");
        dynamicInfo1.setActualDepartureTime("19:47");
        dynamicInfo1.setScheduledArrivalTime("20:03");
        dynamicInfo1.setActualArrivalTime("20:05");
        dynamicInfo1.setGroundTimeMinutes(6);
        dynamicInfo1.setFlightTimeMinutes(16);
        dynamicInfo1.setTotalTimeMinutes(22);
        dynamicInfo1.setLegNumber(1);
        
        TicketRecordExportDto.DynamicInfo dynamicInfo2 = new TicketRecordExportDto.DynamicInfo();
        dynamicInfo2.setSortOrder(2);
        dynamicInfo2.setDepartureAirport("呈贡");
        dynamicInfo2.setArrivalAirport("呈贡");
        dynamicInfo2.setScheduledDepartureTime("20:37");
        dynamicInfo2.setActualDepartureTime("20:40");
        dynamicInfo2.setScheduledArrivalTime("21:15");
        dynamicInfo2.setActualArrivalTime("21:17");
        dynamicInfo2.setGroundTimeMinutes(5);
        dynamicInfo2.setFlightTimeMinutes(35);
        dynamicInfo2.setTotalTimeMinutes(40);
        dynamicInfo2.setLegNumber(2);
        
        dynamicInfoList.add(dynamicInfo1);
        dynamicInfoList.add(dynamicInfo2);
        exportData.setDynamicInfoList(dynamicInfoList);
        
        return exportData;
    }
}
