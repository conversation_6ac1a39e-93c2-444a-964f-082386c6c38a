package com.swcares.sgui.service.export;

import com.swcares.sgui.obj.dto.TicketRecordExportDto;
import com.swcares.sgui.service.export.impl.TicketRecordExportServiceImpl;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 售票记录导出服务快速测试类
 * <AUTHOR> Assistant
 * @date 2025/01/30 11:00
 */
public class TicketRecordExportServiceQuickTest {

    public static void main(String[] args) throws IOException {
        TicketRecordExportServiceImpl service = new TicketRecordExportServiceImpl();
        
        // 构造测试数据
        TicketRecordExportDto exportData = createTestData();
        
        // 生成Word文档
        byte[] documentBytes = service.generateWordDocument(exportData);
        
        // 保存到文件进行验证
        try (FileOutputStream fos = new FileOutputStream("test_ticket_record_fixed.docx")) {
            fos.write(documentBytes);
        }
        
        System.out.println("修复后的Word文档生成成功，文件大小：" + documentBytes.length + " 字节");
        System.out.println("文件保存为：test_ticket_record_fixed.docx");
    }

    /**
     * 创建测试数据
     */
    private static TicketRecordExportDto createTestData() {
        TicketRecordExportDto exportData = new TicketRecordExportDto();
        exportData.setAircraftModel("BELL429");
        exportData.setRegistrationNumber("B-7613");
        exportData.setFlightDate("2025-05-20");
        
        // 创建天气信息
        List<TicketRecordExportDto.WeatherInfo> weatherInfoList = new ArrayList<>();
        TicketRecordExportDto.WeatherInfo weatherInfo = new TicketRecordExportDto.WeatherInfo();
        weatherInfo.setSortOrder(1);
        weatherInfo.setDepartureAirport("呈贡");
        weatherInfo.setWeather("晴天");
        weatherInfo.setCloudHeight("无影响");
        weatherInfo.setTemperature("26");
        weatherInfo.setWindDirection("100");
        weatherInfo.setWindSpeed("4");
        weatherInfo.setVisibility("9999");
        weatherInfo.setQnh("1004");
        weatherInfo.setRunway("徐建军");
        weatherInfo.setSideRunway("王昂");
        
        weatherInfo.setArrivalAirport("呈贡");
        weatherInfo.setArrivalWeather("晴天");
        weatherInfo.setArrivalCloudHeight("无影响");
        weatherInfo.setArrivalTemperature("26");
        weatherInfo.setArrivalWindDirection("100");
        weatherInfo.setArrivalWindSpeed("4");
        weatherInfo.setArrivalVisibility("9999");
        weatherInfo.setArrivalQnh("1004");
        weatherInfo.setArrivalRunway("徐建军");
        weatherInfo.setArrivalSideRunway("王昂");
        
        weatherInfoList.add(weatherInfo);
        exportData.setWeatherInfoList(weatherInfoList);
        
        // 创建动态信息
        List<TicketRecordExportDto.DynamicInfo> dynamicInfoList = new ArrayList<>();
        
        TicketRecordExportDto.DynamicInfo dynamicInfo1 = new TicketRecordExportDto.DynamicInfo();
        dynamicInfo1.setSortOrder(1);
        dynamicInfo1.setDepartureAirport("呈贡");
        dynamicInfo1.setArrivalAirport("呈贡");
        dynamicInfo1.setScheduledDepartureTime("19:43");
        dynamicInfo1.setActualDepartureTime("19:47");
        dynamicInfo1.setScheduledArrivalTime("20:03");
        dynamicInfo1.setActualArrivalTime("20:05");
        dynamicInfo1.setGroundTimeMinutes(6);
        dynamicInfo1.setFlightTimeMinutes(16);
        dynamicInfo1.setTotalTimeMinutes(22);
        dynamicInfo1.setLegNumber(1);
        
        TicketRecordExportDto.DynamicInfo dynamicInfo2 = new TicketRecordExportDto.DynamicInfo();
        dynamicInfo2.setSortOrder(2);
        dynamicInfo2.setDepartureAirport("呈贡");
        dynamicInfo2.setArrivalAirport("呈贡");
        dynamicInfo2.setScheduledDepartureTime("20:37");
        dynamicInfo2.setActualDepartureTime("20:40");
        dynamicInfo2.setScheduledArrivalTime("21:15");
        dynamicInfo2.setActualArrivalTime("21:17");
        dynamicInfo2.setGroundTimeMinutes(5);
        dynamicInfo2.setFlightTimeMinutes(35);
        dynamicInfo2.setTotalTimeMinutes(40);
        dynamicInfo2.setLegNumber(2);
        
        dynamicInfoList.add(dynamicInfo1);
        dynamicInfoList.add(dynamicInfo2);
        exportData.setDynamicInfoList(dynamicInfoList);
        
        return exportData;
    }
}
