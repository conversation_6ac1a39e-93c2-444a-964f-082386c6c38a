/**
 * 售票记录导出功能
 * <AUTHOR> Assistant
 * @date 2025/01/30 10:30
 */

/**
 * 导出售票记录为Word文档
 * @param {Object} exportData 导出数据
 */
function exportTicketRecordToWord(exportData) {
    // 验证数据
    if (!exportData || !exportData.aircraftModel || !exportData.registrationNumber || !exportData.flightDate) {
        alert('请确保基本信息完整');
        return;
    }
    
    // 创建请求
    fetch('/sgui-export/v2/ticket-record/export-word', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(exportData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('导出失败');
        }
        return response.blob();
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `售票记录页_${exportData.flightDate}.docx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        console.log('Word文档导出成功');
    })
    .catch(error => {
        console.error('导出失败:', error);
        alert('导出失败: ' + error.message);
    });
}

/**
 * 示例：构造导出数据并调用导出功能
 */
function exportExample() {
    const exportData = {
        aircraftModel: "BELL429",
        registrationNumber: "B-7613",
        flightDate: "2025-05-20",
        departureWeatherInfoList: [
            {
                sortOrder: 1,
                airport: "呈贡",
                weather: "晴天",
                cloudHeight: "无影响",
                temperature: "26",
                windDirection: "100",
                windSpeed: "4",
                visibility: "9999",
                qnh: "1004",
                runway: "徐建军",
                sideRunway: "王昂"
            }
        ],
        arrivalWeatherInfoList: [
            {
                sortOrder: 1,
                airport: "呈贡",
                weather: "晴天",
                cloudHeight: "无影响",
                temperature: "26",
                windDirection: "100",
                windSpeed: "4",
                visibility: "9999",
                qnh: "1004",
                runway: "徐建军",
                sideRunway: "王昂"
            }
        ],
        dynamicInfoList: [
            {
                sortOrder: 1,
                departureAirport: "呈贡",
                arrivalAirport: "呈贡",
                scheduledDepartureTime: "19:43",
                actualDepartureTime: "19:47",
                scheduledArrivalTime: "20:03",
                actualArrivalTime: "20:05",
                groundTimeMinutes: 6,
                flightTimeMinutes: 16,
                totalTimeMinutes: 22,
                legNumber: 1
            },
            {
                sortOrder: 2,
                departureAirport: "呈贡",
                arrivalAirport: "呈贡",
                scheduledDepartureTime: "20:37",
                actualDepartureTime: "20:40",
                scheduledArrivalTime: "21:15",
                actualArrivalTime: "21:17",
                groundTimeMinutes: 5,
                flightTimeMinutes: 35,
                totalTimeMinutes: 40,
                legNumber: 2
            }
        ]
    };
    
    exportTicketRecordToWord(exportData);
}

/**
 * 从页面表格中收集数据并导出
 * @param {string} tableId 表格ID
 */
function exportFromTable(tableId) {
    // 这里可以根据实际的页面结构来收集数据
    // 示例代码，需要根据实际情况调整
    const table = document.getElementById(tableId);
    if (!table) {
        alert('未找到数据表格');
        return;
    }
    
    // 收集基本信息
    const aircraftModel = document.getElementById('aircraftModel')?.value || '';
    const registrationNumber = document.getElementById('registrationNumber')?.value || '';
    const flightDate = document.getElementById('flightDate')?.value || '';
    
    // 收集天气信息和动态信息
    // 这里需要根据实际的表格结构来实现数据收集逻辑
    
    const exportData = {
        aircraftModel,
        registrationNumber,
        flightDate,
        departureWeatherInfoList: [], // 从表格收集始发地天气信息
        arrivalWeatherInfoList: [],   // 从表格收集目的地天气信息
        dynamicInfoList: []           // 从表格收集动态信息
    };
    
    exportTicketRecordToWord(exportData);
}
