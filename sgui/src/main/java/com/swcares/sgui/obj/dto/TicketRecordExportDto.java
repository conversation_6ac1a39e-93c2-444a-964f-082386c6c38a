package com.swcares.sgui.obj.dto;

import lombok.Data;
import java.util.List;

/**
 * 售票记录导出数据传输对象
 * <AUTHOR> Assistant
 * @date 2025/01/30 10:30
 */
@Data
public class TicketRecordExportDto {
    
    /**
     * 基本信息
     */
    private String aircraftModel;  // 机型
    private String registrationNumber;  // 注册号
    private String flightDate;  // 飞行日期
    
    /**
     * 天气信息列表
     */
    private List<WeatherInfo> weatherInfoList;
    
    /**
     * 动态信息列表
     */
    private List<DynamicInfo> dynamicInfoList;
    
    /**
     * 天气信息内部类
     */
    @Data
    public static class WeatherInfo {
        private Integer sortOrder;  // 批次
        private String departureAirport;  // 始发地
        private String weather;  // 天气
        private String cloudHeight;  // 云高(m)
        private String temperature;  // 温度(℃)
        private String windDirection;  // 风向(°)
        private String windSpeed;  // 风速(m/s)
        private String visibility;  // 能见度(m)
        private String qnh;  // QNH(hPa)
        private String runway;  // 正导航
        private String sideRunway;  // 副导航
        private String arrivalAirport;  // 目的地
        private String arrivalWeather;  // 目的地天气
        private String arrivalCloudHeight;  // 目的地云高(m)
        private String arrivalTemperature;  // 目的地温度(℃)
        private String arrivalWindDirection;  // 目的地风向(°)
        private String arrivalWindSpeed;  // 目的地风速(m/s)
        private String arrivalVisibility;  // 目的地能见度(m)
        private String arrivalQnh;  // 目的地QNH(hPa)
        private String arrivalRunway;  // 目的地正导航
        private String arrivalSideRunway;  // 目的地副导航
    }
    
    /**
     * 动态信息内部类
     */
    @Data
    public static class DynamicInfo {
        private Integer sortOrder;  // 批次
        private String departureAirport;  // 始发地
        private String arrivalAirport;  // 目的地
        private String scheduledDepartureTime;  // 开车时刻
        private String actualDepartureTime;  // 起飞时刻
        private String scheduledArrivalTime;  // 着陆时刻
        private String actualArrivalTime;  // 关车时刻
        private Integer groundTimeMinutes;  // 地面时间(分钟)
        private Integer flightTimeMinutes;  // 空中时间(分钟)
        private Integer totalTimeMinutes;  // 时间小计(分钟)
        private Integer legNumber;  // 架次
    }
}
