package com.swcares.sgui.controller.export;

import com.swcares.sgui.obj.dto.TicketRecordExportDto;
import com.swcares.sgui.service.export.ITicketRecordExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 售票记录导出控制器
 * <AUTHOR> Assistant
 * @date 2025/01/30 10:30
 */
@Slf4j
@RestController
@RequestMapping("/sgui-export/v2")
@Api(tags = "售票记录导出")
public class TicketRecordExportController {

    @Resource
    private ITicketRecordExportService ticketRecordExportService;

    /**
     * 导出售票记录为Word文档
     * @param exportData 导出数据
     * @param response HTTP响应对象
     */
    @PostMapping("/ticket-record/export-word")
    @ApiOperation("导出售票记录Word文档")
    public void exportTicketRecordToWord(@RequestBody TicketRecordExportDto exportData, 
                                       HttpServletResponse response) {
        try {
            log.info("开始导出售票记录Word文档，飞行日期：{}", exportData.getFlightDate());
            ticketRecordExportService.exportTicketRecordToWord(exportData, response);
            log.info("售票记录Word文档导出完成");
        } catch (Exception e) {
            log.error("导出售票记录Word文档失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }
}
