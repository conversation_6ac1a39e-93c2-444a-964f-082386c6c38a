package com.swcares.sgui.service.export;

import com.swcares.sgui.obj.dto.TicketRecordExportDto;
import javax.servlet.http.HttpServletResponse;

/**
 * 售票记录导出服务接口
 * <AUTHOR> Assistant
 * @date 2025/01/30 10:30
 */
public interface ITicketRecordExportService {
    
    /**
     * 导出售票记录为Word文档
     * @param exportData 导出数据
     * @param response HTTP响应对象
     */
    void exportTicketRecordToWord(TicketRecordExportDto exportData, HttpServletResponse response);
    
    /**
     * 生成Word文档字节数组
     * @param exportData 导出数据
     * @return Word文档字节数组
     */
    byte[] generateWordDocument(TicketRecordExportDto exportData);
}
