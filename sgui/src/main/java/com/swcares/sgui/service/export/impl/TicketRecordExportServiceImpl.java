package com.swcares.sgui.service.export.impl;

import com.swcares.sgui.obj.dto.TicketRecordExportDto;
import com.swcares.sgui.service.export.ITicketRecordExportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 售票记录导出服务实现类
 * <AUTHOR> Assistant
 * @date 2025/01/30 10:30
 */
@Slf4j
@Service
public class TicketRecordExportServiceImpl implements ITicketRecordExportService {

    @Override
    public void exportTicketRecordToWord(TicketRecordExportDto exportData, HttpServletResponse response) {
        try {
            // 生成Word文档
            byte[] documentBytes = this.generateWordDocument(exportData);
            
            // 设置响应头
            String fileName = "售票记录页_" + exportData.getFlightDate() + ".docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setContentLength(documentBytes.length);
            
            // 写入响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                outputStream.write(documentBytes);
                outputStream.flush();
            }
            
        } catch (Exception e) {
            log.error("导出售票记录Word文档失败", e);
            throw new RuntimeException("导出Word文档失败", e);
        }
    }

    @Override
    public byte[] generateWordDocument(TicketRecordExportDto exportData) {
        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            // 创建标题
            this.createTitle(document);
            
            // 创建基本信息表格
            this.createBasicInfoTable(document, exportData);
            
            // 创建天气信息表格
            this.createWeatherInfoTable(document, exportData.getWeatherInfoList());
            
            // 创建动态信息表格
            this.createDynamicInfoTable(document, exportData.getDynamicInfoList());
            
            // 写入输出流
            document.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("生成Word文档失败", e);
            throw new RuntimeException("生成Word文档失败", e);
        }
    }

    /**
     * 创建文档标题
     */
    private void createTitle(XWPFDocument document) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("售票记录页");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");
    }

    /**
     * 创建基本信息表格
     */
    private void createBasicInfoTable(XWPFDocument document, TicketRecordExportDto exportData) {
        // 添加空行
        document.createParagraph();

        XWPFTable table = document.createTable(1, 6);
        table.setWidth("100%");

        // 设置表格边框
        this.setTableBorders(table);

        XWPFTableRow row = table.getRow(0);
        this.setCellText(row.getCell(0), "机型", true);
        this.setCellText(row.getCell(1), exportData.getAircraftModel(), false);
        this.setCellText(row.getCell(2), "注册号", true);
        this.setCellText(row.getCell(3), exportData.getRegistrationNumber(), false);
        this.setCellText(row.getCell(4), "飞行日期", true);
        this.setCellText(row.getCell(5), exportData.getFlightDate(), false);
    }

    /**
     * 创建天气信息表格
     */
    private void createWeatherInfoTable(XWPFDocument document, List<TicketRecordExportDto.WeatherInfo> weatherInfoList) {
        if (weatherInfoList == null || weatherInfoList.isEmpty()) {
            return;
        }

        // 添加空行
        document.createParagraph();

        // 创建表格：表头 + 数据行
        XWPFTable table = document.createTable(weatherInfoList.size() * 2 + 1, 11);
        table.setWidth("100%");
        this.setTableBorders(table);

        // 创建表头
        XWPFTableRow headerRow = table.getRow(0);
        this.setCellText(headerRow.getCell(0), "批次", true);
        this.setCellText(headerRow.getCell(1), "始发地", true);
        this.setCellText(headerRow.getCell(2), "天气", true);
        this.setCellText(headerRow.getCell(3), "云高(m)", true);
        this.setCellText(headerRow.getCell(4), "温度(℃)", true);
        this.setCellText(headerRow.getCell(5), "风向(°)", true);
        this.setCellText(headerRow.getCell(6), "风速(m/s)", true);
        this.setCellText(headerRow.getCell(7), "能见度(m)", true);
        this.setCellText(headerRow.getCell(8), "QNH(hPa)", true);
        this.setCellText(headerRow.getCell(9), "正导航", true);
        this.setCellText(headerRow.getCell(10), "副导航", true);

        // 填充数据
        int rowIndex = 1;
        for (TicketRecordExportDto.WeatherInfo weatherInfo : weatherInfoList) {
            // 始发地行
            XWPFTableRow departureRow = table.getRow(rowIndex++);
            this.setCellText(departureRow.getCell(0), String.valueOf(weatherInfo.getSortOrder()), false);
            this.setCellText(departureRow.getCell(1), weatherInfo.getDepartureAirport(), false);
            this.setCellText(departureRow.getCell(2), weatherInfo.getWeather(), false);
            this.setCellText(departureRow.getCell(3), weatherInfo.getCloudHeight(), false);
            this.setCellText(departureRow.getCell(4), weatherInfo.getTemperature(), false);
            this.setCellText(departureRow.getCell(5), weatherInfo.getWindDirection(), false);
            this.setCellText(departureRow.getCell(6), weatherInfo.getWindSpeed(), false);
            this.setCellText(departureRow.getCell(7), weatherInfo.getVisibility(), false);
            this.setCellText(departureRow.getCell(8), weatherInfo.getQnh(), false);
            this.setCellText(departureRow.getCell(9), weatherInfo.getRunway(), false);
            this.setCellText(departureRow.getCell(10), weatherInfo.getSideRunway(), false);

            // 目的地行
            XWPFTableRow arrivalRow = table.getRow(rowIndex++);
            this.setCellText(arrivalRow.getCell(0), String.valueOf(weatherInfo.getSortOrder()), false);
            this.setCellText(arrivalRow.getCell(1), weatherInfo.getArrivalAirport(), false);
            this.setCellText(arrivalRow.getCell(2), weatherInfo.getArrivalWeather(), false);
            this.setCellText(arrivalRow.getCell(3), weatherInfo.getArrivalCloudHeight(), false);
            this.setCellText(arrivalRow.getCell(4), weatherInfo.getArrivalTemperature(), false);
            this.setCellText(arrivalRow.getCell(5), weatherInfo.getArrivalWindDirection(), false);
            this.setCellText(arrivalRow.getCell(6), weatherInfo.getArrivalWindSpeed(), false);
            this.setCellText(arrivalRow.getCell(7), weatherInfo.getArrivalVisibility(), false);
            this.setCellText(arrivalRow.getCell(8), weatherInfo.getArrivalQnh(), false);
            this.setCellText(arrivalRow.getCell(9), weatherInfo.getArrivalRunway(), false);
            this.setCellText(arrivalRow.getCell(10), weatherInfo.getArrivalSideRunway(), false);
        }
    }

    /**
     * 创建动态信息表格
     */
    private void createDynamicInfoTable(XWPFDocument document, List<TicketRecordExportDto.DynamicInfo> dynamicInfoList) {
        if (dynamicInfoList == null || dynamicInfoList.isEmpty()) {
            return;
        }

        // 添加空行
        document.createParagraph();

        // 创建表格：表头 + 数据行 + 合计行
        XWPFTable table = document.createTable(dynamicInfoList.size() + 2, 9);
        table.setWidth("100%");
        this.setTableBorders(table);

        // 创建表头
        XWPFTableRow headerRow = table.getRow(0);
        this.setCellText(headerRow.getCell(0), "批次", true);
        this.setCellText(headerRow.getCell(1), "始发地", true);
        this.setCellText(headerRow.getCell(2), "目的地", true);
        this.setCellText(headerRow.getCell(3), "开车时刻", true);
        this.setCellText(headerRow.getCell(4), "起飞时刻", true);
        this.setCellText(headerRow.getCell(5), "着陆时刻", true);
        this.setCellText(headerRow.getCell(6), "关车时刻", true);
        this.setCellText(headerRow.getCell(7), "地面时间(分钟)", true);
        this.setCellText(headerRow.getCell(8), "空中时间(分钟)", true);

        // 填充数据
        int totalGroundTime = 0;
        int totalFlightTime = 0;
        int totalTime = 0;

        for (int i = 0; i < dynamicInfoList.size(); i++) {
            TicketRecordExportDto.DynamicInfo dynamicInfo = dynamicInfoList.get(i);
            XWPFTableRow dataRow = table.getRow(i + 1);

            this.setCellText(dataRow.getCell(0), String.valueOf(dynamicInfo.getSortOrder()), false);
            this.setCellText(dataRow.getCell(1), dynamicInfo.getDepartureAirport(), false);
            this.setCellText(dataRow.getCell(2), dynamicInfo.getArrivalAirport(), false);
            this.setCellText(dataRow.getCell(3), dynamicInfo.getScheduledDepartureTime(), false);
            this.setCellText(dataRow.getCell(4), dynamicInfo.getActualDepartureTime(), false);
            this.setCellText(dataRow.getCell(5), dynamicInfo.getScheduledArrivalTime(), false);
            this.setCellText(dataRow.getCell(6), dynamicInfo.getActualArrivalTime(), false);
            this.setCellText(dataRow.getCell(7), String.valueOf(dynamicInfo.getGroundTimeMinutes()), false);
            this.setCellText(dataRow.getCell(8), String.valueOf(dynamicInfo.getFlightTimeMinutes()), false);

            // 累计时间
            totalGroundTime += dynamicInfo.getGroundTimeMinutes();
            totalFlightTime += dynamicInfo.getFlightTimeMinutes();
            totalTime += dynamicInfo.getTotalTimeMinutes();
        }

        // 创建合计行
        XWPFTableRow totalRow = table.getRow(dynamicInfoList.size() + 1);
        this.setCellText(totalRow.getCell(0), "剩余油量", true);
        this.setCellText(totalRow.getCell(1), "740L", false);
        this.setCellText(totalRow.getCell(2), "总计", true);
        this.setCellText(totalRow.getCell(3), "", false);
        this.setCellText(totalRow.getCell(4), "", false);
        this.setCellText(totalRow.getCell(5), "", false);
        this.setCellText(totalRow.getCell(6), "", false);
        this.setCellText(totalRow.getCell(7), String.valueOf(totalGroundTime), false);
        this.setCellText(totalRow.getCell(8), String.valueOf(totalFlightTime), false);
    }

    /**
     * 设置表格边框
     */
    private void setTableBorders(XWPFTable table) {
        table.getCTTbl().getTblPr().addNewTblBorders();
        // 这里可以根据需要设置更详细的边框样式
    }

    /**
     * 设置单元格文本
     */
    private void setCellText(XWPFTableCell cell, String text, boolean isBold) {
        // 清除默认段落
        cell.removeParagraph(0);

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        XWPFRun run = paragraph.createRun();
        run.setText(text != null ? text : "");
        run.setBold(isBold);
        run.setFontSize(10);
        run.setFontFamily("宋体");

        // 设置单元格垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }
}
