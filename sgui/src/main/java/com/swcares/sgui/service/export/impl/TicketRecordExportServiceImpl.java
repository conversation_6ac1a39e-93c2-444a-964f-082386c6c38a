package com.swcares.sgui.service.export.impl;

import com.swcares.sgui.obj.dto.TicketRecordExportDto;
import com.swcares.sgui.service.export.ITicketRecordExportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 售票记录导出服务实现类
 * <AUTHOR> Assistant
 * @date 2025/01/30 10:30
 */
@Slf4j
@Service
public class TicketRecordExportServiceImpl implements ITicketRecordExportService {

    @Override
    public void exportTicketRecordToWord(TicketRecordExportDto exportData, HttpServletResponse response) {
        try {
            // 生成Word文档
            byte[] documentBytes = this.generateWordDocument(exportData);
            
            // 设置响应头
            String fileName = "售票记录页_" + exportData.getFlightDate() + ".docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setContentLength(documentBytes.length);
            
            // 写入响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                outputStream.write(documentBytes);
                outputStream.flush();
            }
            
        } catch (Exception e) {
            log.error("导出售票记录Word文档失败", e);
            throw new RuntimeException("导出Word文档失败", e);
        }
    }

    @Override
    public byte[] generateWordDocument(TicketRecordExportDto exportData) {
        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建完整的合并表格
            this.createCompleteTable(document, exportData);

            // 写入输出流
            document.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("生成Word文档失败", e);
            throw new RuntimeException("生成Word文档失败", e);
        }
    }

    /**
     * 创建完整的合并表格
     */
    private void createCompleteTable(XWPFDocument document, TicketRecordExportDto exportData) {
        List<TicketRecordExportDto.WeatherInfo> weatherInfoList = exportData.getWeatherInfoList();
        List<TicketRecordExportDto.DynamicInfo> dynamicInfoList = exportData.getDynamicInfoList();

        // 计算表格总行数：标题行(1) + 基本信息行(1) + 气象信息标题行(1) + 气象信息表头行(1) + 气象信息数据行(weatherInfoList.size() * 2)
        // + 动态信息标题行(1) + 动态信息表头行(1) + 动态信息数据行(dynamicInfoList.size()) + 合计行(1)
        int weatherDataRows = (weatherInfoList != null) ? weatherInfoList.size() * 2 : 0;
        int dynamicDataRows = (dynamicInfoList != null) ? dynamicInfoList.size() : 0;
        int totalRows = 1 + 1 + 1 + 1 + weatherDataRows + 1 + 1 + dynamicDataRows + 1;

        // 创建表格，使用11列（动态信息表格的列数最多）
        XWPFTable table = document.createTable(totalRows, 11);
        table.setWidth("100%");
        this.setTableBorders(table);

        int currentRow = 0;

        // 第1行：标题行 - "售票记录页"
        XWPFTableRow titleRow = table.getRow(currentRow++);
        this.mergeCellsHorizontally(table, 0, 0, 10); // 合并第一行的所有列
        this.setCellText(titleRow.getCell(0), "售票记录页", true, 16);

        // 第2行：基本信息行
        XWPFTableRow basicInfoRow = table.getRow(currentRow++);
        this.setCellText(basicInfoRow.getCell(0), "机型", true);
        this.setCellText(basicInfoRow.getCell(1), exportData.getAircraftModel(), false);
        this.setCellText(basicInfoRow.getCell(2), "注册号", true);
        this.setCellText(basicInfoRow.getCell(3), exportData.getRegistrationNumber(), false);
        this.setCellText(basicInfoRow.getCell(4), "飞行日期", true);
        this.setCellText(basicInfoRow.getCell(5), exportData.getFlightDate(), false);
        // 清空剩余单元格
        for (int i = 6; i < 11; i++) {
            this.setCellText(basicInfoRow.getCell(i), "", false);
        }

        // 第3行：气象信息标题行
        XWPFTableRow weatherTitleRow = table.getRow(currentRow++);
        this.mergeCellsHorizontally(table, currentRow - 1, 0, 10);
        this.setCellText(weatherTitleRow.getCell(0), "气象信息", true, 14);

        // 第4行：气象信息表头
        XWPFTableRow weatherHeaderRow = table.getRow(currentRow++);
        this.setCellText(weatherHeaderRow.getCell(0), "批次", true);
        this.setCellText(weatherHeaderRow.getCell(1), "始发地", true);
        this.setCellText(weatherHeaderRow.getCell(2), "天气", true);
        this.setCellText(weatherHeaderRow.getCell(3), "云高(m)", true);
        this.setCellText(weatherHeaderRow.getCell(4), "温度(℃)", true);
        this.setCellText(weatherHeaderRow.getCell(5), "风向(°)", true);
        this.setCellText(weatherHeaderRow.getCell(6), "风速(m/s)", true);
        this.setCellText(weatherHeaderRow.getCell(7), "能见度(m)", true);
        this.setCellText(weatherHeaderRow.getCell(8), "QNH(hPa)", true);
        this.setCellText(weatherHeaderRow.getCell(9), "正导航", true);
        this.setCellText(weatherHeaderRow.getCell(10), "副导航", true);

        // 气象信息数据行
        if (weatherInfoList != null) {
            for (TicketRecordExportDto.WeatherInfo weatherInfo : weatherInfoList) {
                // 始发地行
                XWPFTableRow departureRow = table.getRow(currentRow++);
                this.setCellText(departureRow.getCell(0), String.valueOf(weatherInfo.getSortOrder()), false);
                this.setCellText(departureRow.getCell(1), weatherInfo.getDepartureAirport(), false);
                this.setCellText(departureRow.getCell(2), weatherInfo.getWeather(), false);
                this.setCellText(departureRow.getCell(3), weatherInfo.getCloudHeight(), false);
                this.setCellText(departureRow.getCell(4), weatherInfo.getTemperature(), false);
                this.setCellText(departureRow.getCell(5), weatherInfo.getWindDirection(), false);
                this.setCellText(departureRow.getCell(6), weatherInfo.getWindSpeed(), false);
                this.setCellText(departureRow.getCell(7), weatherInfo.getVisibility(), false);
                this.setCellText(departureRow.getCell(8), weatherInfo.getQnh(), false);
                this.setCellText(departureRow.getCell(9), weatherInfo.getRunway(), false);
                this.setCellText(departureRow.getCell(10), weatherInfo.getSideRunway(), false);

                // 目的地行
                XWPFTableRow arrivalRow = table.getRow(currentRow++);
                this.setCellText(arrivalRow.getCell(0), String.valueOf(weatherInfo.getSortOrder()), false);
                this.setCellText(arrivalRow.getCell(1), weatherInfo.getArrivalAirport(), false);
                this.setCellText(arrivalRow.getCell(2), weatherInfo.getArrivalWeather(), false);
                this.setCellText(arrivalRow.getCell(3), weatherInfo.getArrivalCloudHeight(), false);
                this.setCellText(arrivalRow.getCell(4), weatherInfo.getArrivalTemperature(), false);
                this.setCellText(arrivalRow.getCell(5), weatherInfo.getArrivalWindDirection(), false);
                this.setCellText(arrivalRow.getCell(6), weatherInfo.getArrivalWindSpeed(), false);
                this.setCellText(arrivalRow.getCell(7), weatherInfo.getArrivalVisibility(), false);
                this.setCellText(arrivalRow.getCell(8), weatherInfo.getArrivalQnh(), false);
                this.setCellText(arrivalRow.getCell(9), weatherInfo.getArrivalRunway(), false);
                this.setCellText(arrivalRow.getCell(10), weatherInfo.getArrivalSideRunway(), false);
            }
        }

        // 动态信息标题行
        XWPFTableRow dynamicTitleRow = table.getRow(currentRow++);
        this.mergeCellsHorizontally(table, currentRow - 1, 0, 10);
        this.setCellText(dynamicTitleRow.getCell(0), "动态信息", true, 14);

        // 动态信息表头
        XWPFTableRow dynamicHeaderRow = table.getRow(currentRow++);
        this.setCellText(dynamicHeaderRow.getCell(0), "批次", true);
        this.setCellText(dynamicHeaderRow.getCell(1), "始发地", true);
        this.setCellText(dynamicHeaderRow.getCell(2), "目的地", true);
        this.setCellText(dynamicHeaderRow.getCell(3), "开车时刻", true);
        this.setCellText(dynamicHeaderRow.getCell(4), "起飞时刻", true);
        this.setCellText(dynamicHeaderRow.getCell(5), "着陆时刻", true);
        this.setCellText(dynamicHeaderRow.getCell(6), "关车时刻", true);
        this.setCellText(dynamicHeaderRow.getCell(7), "地面时间(分钟)", true);
        this.setCellText(dynamicHeaderRow.getCell(8), "空中时间(分钟)", true);
        this.setCellText(dynamicHeaderRow.getCell(9), "时间小计(分钟)", true);
        this.setCellText(dynamicHeaderRow.getCell(10), "架次", true);

        // 动态信息数据行和统计
        int totalGroundTime = 0;
        int totalFlightTime = 0;
        int totalTime = 0;
        int totalLegs = 0;

        if (dynamicInfoList != null) {
            for (TicketRecordExportDto.DynamicInfo dynamicInfo : dynamicInfoList) {
                XWPFTableRow dataRow = table.getRow(currentRow++);

                this.setCellText(dataRow.getCell(0), String.valueOf(dynamicInfo.getSortOrder()), false);
                this.setCellText(dataRow.getCell(1), dynamicInfo.getDepartureAirport(), false);
                this.setCellText(dataRow.getCell(2), dynamicInfo.getArrivalAirport(), false);
                this.setCellText(dataRow.getCell(3), dynamicInfo.getScheduledDepartureTime(), false);
                this.setCellText(dataRow.getCell(4), dynamicInfo.getActualDepartureTime(), false);
                this.setCellText(dataRow.getCell(5), dynamicInfo.getScheduledArrivalTime(), false);
                this.setCellText(dataRow.getCell(6), dynamicInfo.getActualArrivalTime(), false);
                this.setCellText(dataRow.getCell(7), String.valueOf(dynamicInfo.getGroundTimeMinutes()), false);
                this.setCellText(dataRow.getCell(8), String.valueOf(dynamicInfo.getFlightTimeMinutes()), false);
                this.setCellText(dataRow.getCell(9), String.valueOf(dynamicInfo.getTotalTimeMinutes()), false);
                this.setCellText(dataRow.getCell(10), String.valueOf(dynamicInfo.getLegNumber()), false);

                // 累计统计
                totalGroundTime += dynamicInfo.getGroundTimeMinutes();
                totalFlightTime += dynamicInfo.getFlightTimeMinutes();
                totalTime += dynamicInfo.getTotalTimeMinutes();
                totalLegs = Math.max(totalLegs, dynamicInfo.getLegNumber());
            }
        }

        // 合计行
        XWPFTableRow totalRow = table.getRow(currentRow);
        // 剩余油量：合并第1列和第2列
        this.mergeCellsHorizontally(table, currentRow, 0, 1);
        this.setCellText(totalRow.getCell(0), "剩余油量", true);

        // 剩余油量数据：合并第3列和第4列
        this.mergeCellsHorizontally(table, currentRow, 2, 3);
        this.setCellText(totalRow.getCell(2), "740L", false);

        // 总计：合并第5列、第6列、第7列
        this.mergeCellsHorizontally(table, currentRow, 4, 6);
        this.setCellText(totalRow.getCell(4), "总计", true);

        // 总计数据
        this.setCellText(totalRow.getCell(7), String.valueOf(totalGroundTime), false);
        this.setCellText(totalRow.getCell(8), String.valueOf(totalFlightTime), false);
        this.setCellText(totalRow.getCell(9), String.valueOf(totalTime), false);
        this.setCellText(totalRow.getCell(10), String.valueOf(totalLegs), false);
    }

    /**
     * 水平合并单元格
     */
    private void mergeCellsHorizontally(XWPFTable table, int row, int fromCol, int toCol) {
        for (int colIndex = fromCol; colIndex <= toCol; colIndex++) {
            XWPFTableCell cell = table.getRow(row).getCell(colIndex);
            if (colIndex == fromCol) {
                // 第一个单元格设置为重新开始
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // 其他单元格设置为继续
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 设置表格边框
     */
    private void setTableBorders(XWPFTable table) {
        table.getCTTbl().getTblPr().addNewTblBorders();
        // 这里可以根据需要设置更详细的边框样式
    }

    /**
     * 设置单元格文本
     */
    private void setCellText(XWPFTableCell cell, String text, boolean isBold) {
        this.setCellText(cell, text, isBold, 10);
    }

    /**
     * 设置单元格文本（带字体大小）
     */
    private void setCellText(XWPFTableCell cell, String text, boolean isBold, int fontSize) {
        // 清除默认段落
        cell.removeParagraph(0);

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        XWPFRun run = paragraph.createRun();
        run.setText(text != null ? text : "");
        run.setBold(isBold);
        run.setFontSize(fontSize);
        run.setFontFamily("宋体");

        // 设置单元格垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }
}
